// lib/widgets/category_card.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'svg_icon.dart';
import '../providers/theme_provider.dart';

class CategoryCard extends StatelessWidget {
  final String title;
  final IconData? icon;
  final String? iconAsset;
  final Color color;
  final VoidCallback onTap;

  const CategoryCard({
    super.key,
    required this.title,
    this.icon,
    this.iconAsset,
    required this.color,
    required this.onTap,
  });

  Widget _buildIcon() {
    // Check if we have an asset path and if it's an SVG
    if (iconAsset != null) {
      if (iconAsset!.toLowerCase().endsWith('.svg')) {
        // Use SvgIcon for SVG files
        return SvgIcon(
          assetPath: iconAsset!,
          width: 35,
          height: 35,
          color: Colors.white,
          fallbackIcon: _getFallbackIcon(),
        );
      } else {
        // Use Image.asset for PNG/other image files
        return Image.asset(
          iconAsset!,
          width: 35,
          height: 35,
          fit: BoxFit.contain,
          color: Colors.white,
          colorBlendMode: BlendMode.srcIn,
          cacheWidth: 35,
          cacheHeight: 35,
          filterQuality: FilterQuality.low,
          errorBuilder: (context, error, stackTrace) {
            debugPrint('Asset failed to load: $iconAsset, using fallback icon');
            IconData fallbackIcon = _getFallbackIcon();
            return Icon(fallbackIcon, size: 35, color: Colors.white);
          },
        );
      }
    } else {
      return Icon(icon ?? Icons.category, size: 35, color: Colors.white);
    }
  }

  IconData _getFallbackIcon() {
    switch (title.toLowerCase()) {
      case 'bold':
        return Icons.flash_on;
      case 'bad':
        return Icons.thumb_down;
      case 'cute':
        return Icons.favorite;
      case 'clever':
        return Icons.lightbulb;
      case 'genius':
        return Icons.psychology;
      case 'dirty':
        return Icons.whatshot;
      case 'flirty':
        return Icons.face;
      case 'hookup':
        return Icons.nightlife;
      case 'romantic':
        return Icons.favorite_border;
      case 'funny':
        return Icons.emoji_emotions;
      case 'nerd':
        return Icons.science;
      case 'food':
        return Icons.restaurant;
      default:
        return Icons.category;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Container(
          margin: EdgeInsets.symmetric(vertical: 12, horizontal: 8),
          height: 90,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                color.withValues(alpha: 0.8),
                color,
                color.withValues(alpha: 0.9),
              ],
            ),
            boxShadow: [
              // Enhanced 3D shadow with multiple layers
              ...themeProvider.getCardShadow(elevation: 12.0),
              // Additional colored shadow for depth
              BoxShadow(
                color: color.withValues(alpha: 0.4),
                spreadRadius: 0,
                blurRadius: 16,
                offset: const Offset(0, 8),
              ),
              // Subtle glow effect
              BoxShadow(
                color: color.withValues(alpha: 0.2),
                spreadRadius: 2,
                blurRadius: 24,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(20),
              onTap: onTap,
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 20, vertical: 16),
                child: Row(
                  children: [
                    Container(
                      width: 60,
                      height: 60,
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(15),
                        boxShadow: [
                          // Enhanced icon container shadow
                          BoxShadow(
                            color: Colors.black.withValues(
                              alpha: themeProvider.isDarkMode ? 0.3 : 0.15,
                            ),
                            spreadRadius: 0,
                            blurRadius: 8,
                            offset: const Offset(0, 4),
                          ),
                          BoxShadow(
                            color: Colors.white.withValues(
                              alpha: themeProvider.isDarkMode ? 0.1 : 0.6,
                            ),
                            spreadRadius: 0,
                            blurRadius: 2,
                            offset: const Offset(0, -1),
                          ),
                        ],
                      ),
                      child: Center(child: _buildIcon()),
                    ),
                    SizedBox(width: 20),
                    Expanded(
                      child: Text(
                        title,
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                          shadows: [
                            Shadow(
                              color: Colors.black.withValues(alpha: 0.3),
                              offset: Offset(1, 1),
                              blurRadius: 2,
                            ),
                          ],
                        ),
                      ),
                    ),
                    Icon(
                      Icons.arrow_forward_ios,
                      color: Colors.white.withValues(alpha: 0.7),
                      size: 20,
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
