// lib/screens/settings_screen.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../widgets/app_drawer.dart';
import '../widgets/custom_app_bar.dart';
import '../providers/theme_provider.dart';

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Scaffold(
          appBar: Custom3DAppBar(title: "Settings"),
          drawer: AppDrawer(),
          body: ListView(
            padding: EdgeInsets.all(16),
            children: [
              // Appearance Section
              _buildSectionHeader("Appearance"),
              _buildSettingTile(
                context: context,
                icon: themeProvider.isDarkMode
                    ? Icons.dark_mode
                    : Icons.light_mode,
                title: "Dark Mode",
                subtitle: themeProvider.isDarkMode
                    ? "Dark theme enabled"
                    : "Light theme enabled",
                value: themeProvider.isDarkMode,
                onChanged: (value) {
                  themeProvider.setDarkMode(value);
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        value ? 'Dark mode enabled' : 'Light mode enabled',
                      ),
                    ),
                  );
                },
              ),

              SizedBox(height: 24),

              // Notifications Section
              _buildSectionHeader("Notifications"),
              _buildSettingTile(
                context: context,
                icon: themeProvider.pushNotifications
                    ? Icons.notifications_active
                    : Icons.notifications_off,
                title: "Push Notifications",
                subtitle: themeProvider.pushNotifications
                    ? "Receive notifications for new content"
                    : "Notifications are disabled",
                value: themeProvider.pushNotifications,
                onChanged: (value) {
                  themeProvider.setPushNotifications(value);
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        value
                            ? 'Push notifications enabled'
                            : 'Push notifications disabled',
                      ),
                    ),
                  );
                },
              ),

              SizedBox(height: 24),

              // Sound Section
              _buildSectionHeader("Sound"),
              _buildSettingTile(
                context: context,
                icon: themeProvider.tapSound
                    ? Icons.volume_up
                    : Icons.volume_off,
                title: "Tap Sound",
                subtitle: themeProvider.tapSound
                    ? "Play sound when tapping posts"
                    : "Tap sounds are disabled",
                value: themeProvider.tapSound,
                onChanged: (value) {
                  themeProvider.setTapSound(value);
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        value ? 'Tap sounds enabled' : 'Tap sounds disabled',
                      ),
                    ),
                  );
                },
              ),

              SizedBox(height: 32),

              // Additional Settings Section
              _buildSectionHeader("Other"),

              // Reset Settings
              Container(
                margin: EdgeInsets.symmetric(vertical: 8),
                decoration: BoxDecoration(
                  color: Theme.of(context).cardColor,
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(color: Colors.grey.shade200),
                ),
                child: ListTile(
                  leading: Container(
                    padding: EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.red.shade100,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.restore,
                      color: Colors.red.shade600,
                      size: 24,
                    ),
                  ),
                  title: Text(
                    "Reset Settings",
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                  ),
                  subtitle: Text(
                    "Reset all settings to default values",
                    style: TextStyle(fontSize: 14),
                  ),
                  trailing: Icon(Icons.arrow_forward_ios, size: 16),
                  onTap: () {
                    _showResetDialog(context, themeProvider);
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: EdgeInsets.only(bottom: 12, left: 4),
      child: Text(
        title,
        style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
      ),
    );
  }

  Widget _buildSettingTile({
    required BuildContext context,
    required IconData icon,
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 8),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: ListTile(
        leading: Container(
          padding: EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.purple.shade100,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: Colors.purple.shade600, size: 24),
        ),
        title: Text(
          title,
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
        ),
        subtitle: Text(subtitle, style: TextStyle(fontSize: 14)),
        trailing: Switch(
          value: value,
          onChanged: onChanged,
          activeColor: Colors.purple.shade600,
          activeTrackColor: Colors.purple.shade200,
          inactiveThumbColor: Colors.grey.shade400,
          inactiveTrackColor: Colors.grey.shade300,
        ),
      ),
    );
  }

  void _showResetDialog(BuildContext context, ThemeProvider themeProvider) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text("Reset Settings"),
          content: Text(
            "Are you sure you want to reset all settings to their default values?",
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text("Cancel"),
            ),
            TextButton(
              onPressed: () {
                themeProvider.resetSettings();
                Navigator.of(context).pop();
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('Settings reset to default')),
                );
              },
              child: Text("Reset", style: TextStyle(color: Colors.red)),
            ),
          ],
        );
      },
    );
  }
}
