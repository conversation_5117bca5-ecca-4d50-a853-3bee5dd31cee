// lib/providers/theme_provider.dart
import 'package:flutter/material.dart';
import '../utils/shadow_utils.dart';

class ThemeProvider extends ChangeNotifier {
  bool _isDarkMode = false;
  bool _pushNotifications = true;
  bool _tapSound = true;

  bool get isDarkMode => _isDarkMode;
  bool get pushNotifications => _pushNotifications;
  bool get tapSound => _tapSound;

  ThemeData get currentTheme => _isDarkMode ? _darkTheme : _lightTheme;

  // Light Theme
  static final ThemeData _lightTheme = ThemeData(
    useMaterial3: true,
    brightness: Brightness.light,
    primarySwatch: Colors.purple,
    primaryColor: Colors.purple.shade600,
    scaffoldBackgroundColor: Colors.white,
    appBarTheme: AppBarTheme(
      backgroundColor: Colors.white,
      foregroundColor: Colors.black,
      elevation: 8,
      shadowColor: Colors.black.withValues(alpha: 0.15),
      titleTextStyle: TextStyle(
        fontSize: 24,
        fontWeight: FontWeight.bold,
        color: Colors.black,
      ),
    ),
    drawerTheme: DrawerThemeData(backgroundColor: Colors.white),
    cardTheme: CardThemeData(color: Colors.white),
    textTheme: TextTheme(
      bodyLarge: TextStyle(color: Colors.black87),
      bodyMedium: TextStyle(color: Colors.black87),
      titleLarge: TextStyle(color: Colors.black87),
    ),
  );

  // Dark Theme
  static final ThemeData _darkTheme = ThemeData(
    useMaterial3: true,
    brightness: Brightness.dark,
    primarySwatch: Colors.purple,
    primaryColor: Colors.purple.shade400,
    scaffoldBackgroundColor: Colors.grey.shade900,
    appBarTheme: AppBarTheme(
      backgroundColor: Colors.grey.shade900,
      foregroundColor: Colors.white,
      elevation: 8,
      shadowColor: Colors.black.withValues(alpha: 0.4),
      titleTextStyle: TextStyle(
        fontSize: 24,
        fontWeight: FontWeight.bold,
        color: Colors.white,
      ),
    ),
    drawerTheme: DrawerThemeData(backgroundColor: Colors.grey.shade800),
    cardTheme: CardThemeData(color: Colors.grey.shade800),
    textTheme: TextTheme(
      bodyLarge: TextStyle(color: Colors.white),
      bodyMedium: TextStyle(color: Colors.white),
      titleLarge: TextStyle(color: Colors.white),
    ),
  );

  void toggleDarkMode() {
    _isDarkMode = !_isDarkMode;
    notifyListeners();
  }

  void setDarkMode(bool value) {
    _isDarkMode = value;
    notifyListeners();
  }

  void setPushNotifications(bool value) {
    _pushNotifications = value;
    notifyListeners();
  }

  void setTapSound(bool value) {
    _tapSound = value;
    notifyListeners();
  }

  void resetSettings() {
    _isDarkMode = false;
    _pushNotifications = true;
    _tapSound = true;
    notifyListeners();
  }

  // 3D Shadow helper methods
  List<BoxShadow> getAppBarShadow() {
    return ShadowUtils.createAppBarShadow(isDarkMode: _isDarkMode);
  }

  List<BoxShadow> getCardShadow({double elevation = 8.0}) {
    return ShadowUtils.createFloatingCardShadow(
      isDarkMode: _isDarkMode,
      elevation: elevation,
    );
  }

  List<BoxShadow> getButtonShadow({bool isPressed = false}) {
    return ShadowUtils.createButtonShadow(
      isDarkMode: _isDarkMode,
      isPressed: isPressed,
    );
  }

  List<BoxShadow> getDrawerShadow() {
    return ShadowUtils.createDrawerShadow(isDarkMode: _isDarkMode);
  }

  List<BoxShadow> get3DShadow({double elevation = 4.0}) {
    return ShadowUtils.create3DShadow(
      elevation: elevation,
      isDarkMode: _isDarkMode,
    );
  }
}
